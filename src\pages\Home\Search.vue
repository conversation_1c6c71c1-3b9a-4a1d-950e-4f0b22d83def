<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
</script>

<template>
  <div class="search">
    <el-autocomplete
      clearable
      placeholder="请输入医院名称"
      class="input"
      size="large" />
    <el-button type="primary" :icon="Search" size="large"></el-button>
  </div>
</template>

<style scoped lang="scss">
.search {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  :deep(.el-autocomplete) {
    width: 600px;
    margin-right: 10px;
    .el-input__wrapper {
      height: 40px; // 自定义输入框高度
    }
  }

  :deep(.el-button) {
    height: 40px; // 自定义按钮高度
  }
}
</style>
