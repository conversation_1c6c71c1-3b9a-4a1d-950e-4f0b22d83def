<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
</script>

<template>
  <div class="search">
    <el-autocomplete
      clearable
      placeholder="请输入医院名称"
      class="input" />
    <el-button type="primary" :icon="Search" class="search-btn"></el-button>
  </div>
</template>

<style scoped lang="scss">
.search {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  :deep(.el-autocomplete) {
    width: 600px;
    margin-right: 10px;
  }

  .search-btn {
    background-color: #409EFF;
    border-color: #409EFF;
    color: white;
  }

  .search-btn:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }

}
</style>
