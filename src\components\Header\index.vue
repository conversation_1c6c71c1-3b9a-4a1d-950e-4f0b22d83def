<script setup lang="ts">
</script>

<template>
  <div class="header">
    <div class="content">
      <div class="logo">
        <img src="@/assets/images/logo.png" alt="">
        <span>尚医通 预约挂号统一平台</span>
      </div>
      <div class="navigator">
        <a href="">帮助中心</a>
        <a href="">登录/注册</a>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.header {
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.content {
  width: 1200px;
  height: 70px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;

  img {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }

  span {
    font-size: 20px;
    color: #55a6fe;
    cursor: default;
    user-select: none;
  }
}

.navigator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;

  a {
    text-decoration: none;
    color: #aaa;
    user-select: none;
  }

  a:hover {
    color: #55a6fe;
  }
}
</style>
