import{_ as r,c as n,r as t,o as s,w as a,a as l,F as _,b as u,d as i,e as m,t as d}from"./index-C0y2Jf5F.js";const f={},p={class:"small justify-center",text:"2xl"};function x(h,k){const o=t("el-carousel-item"),c=t("el-carousel");return s(),n(c,{height:"150px"},{default:a(()=>[(s(),l(_,null,u(4,e=>i(o,{key:e},{default:a(()=>[m("h3",p,d(e),1)]),_:2},1024)),64))]),_:1})}const g=r(f,[["render",x]]);export{g as default};
